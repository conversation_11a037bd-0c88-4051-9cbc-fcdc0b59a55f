using System.ComponentModel.DataAnnotations;

namespace ShiningCMusicCommon.Models
{
    public class UserRole
    {
        [Key]
        public int ID { get; set; }

        [StringLength(50)]
        public string? Description { get; set; }

        [Range(1, 100)]
        public int AccessLevel { get; set; }

        // Navigation properties
        public virtual ICollection<User> Users { get; set; } = new List<User>();
    }
}
