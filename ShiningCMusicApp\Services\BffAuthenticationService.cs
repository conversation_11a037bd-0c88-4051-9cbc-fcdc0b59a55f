using System.Text.Json;
using System.Text;
using ShiningCMusicCommon.Models;
using Microsoft.AspNetCore.Components.WebAssembly.Http;

namespace ShiningCMusicApp.Services
{
    public interface IWebBffAuthenticationService
    {
        Task<bool> LoginAsync(string loginName, string password);
        Task<bool> LogoutAsync();
        Task<User?> GetCurrentUserAsync();
        Task<bool> IsAuthenticatedAsync();
    }

    public class BffAuthenticationService : IWebBffAuthenticationService
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;
        private readonly ILogger<BffAuthenticationService> _logger;

        public BffAuthenticationService(HttpClient httpClient, BffConfiguration bffConfig, ILogger<BffAuthenticationService> logger)
        {
            _httpClient = httpClient;
            _baseUrl = bffConfig.BaseUrl.TrimEnd('/');
            _logger = logger;
        }

        public async Task<bool> LoginAsync(string loginName, string password)
        {
            try
            {
                var loginRequest = new
                {
                    LoginName = loginName,
                    Password = password
                };

                var json = JsonSerializer.Serialize(loginRequest);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/auth/login")
                {
                    Content = content
                };
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var loginResponse = JsonSerializer.Deserialize<LoginResponse>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    _logger.LogInformation("Login successful for user: {LoginName}", loginName);
                    return loginResponse?.Success ?? false;
                }

                _logger.LogWarning("Login failed for user: {LoginName}, Status: {StatusCode}", loginName, response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login for user: {LoginName}", loginName);
                return false;
            }
        }

        public async Task<bool> LogoutAsync()
        {
            try
            {
                var request = new HttpRequestMessage(HttpMethod.Post, $"{_baseUrl}/auth/logout");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Logout successful");
                    return true;
                }

                _logger.LogWarning("Logout failed, Status: {StatusCode}", response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during logout");
                return false;
            }
        }

        public async Task<User?> GetCurrentUserAsync()
        {
            try
            {
                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/auth/user");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var userInfo = JsonSerializer.Deserialize<UserInfo>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    if (userInfo != null)
                    {
                        // Convert UserInfo to User model
                        return new User
                        {
                            LoginName = userInfo.LoginName ?? string.Empty,
                            UserName = userInfo.UserName ?? string.Empty,
                            RoleDescription = userInfo.RoleName ?? string.Empty,
                            RoleId = userInfo.RoleId,
                            RoleAccessLevel = userInfo.AccessLevel
                        };
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting current user");
                return null;
            }
        }

        public async Task<bool> IsAuthenticatedAsync()
        {
            try
            {
                _logger.LogInformation("Checking authentication status at: {Url}", $"{_baseUrl}/auth/check");

                var request = new HttpRequestMessage(HttpMethod.Get, $"{_baseUrl}/auth/check");
                request.SetBrowserRequestCredentials(BrowserRequestCredentials.Include);

                var response = await _httpClient.SendAsync(request);

                _logger.LogInformation("Authentication check response: {StatusCode}", response.StatusCode);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var authCheck = JsonSerializer.Deserialize<AuthenticationCheck>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    var isAuth = authCheck?.IsAuthenticated ?? false;
                    _logger.LogInformation("Authentication status: {IsAuthenticated}", isAuth);
                    return isAuth;
                }

                _logger.LogWarning("Authentication check failed with status: {StatusCode}", response.StatusCode);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking authentication status at URL: {Url}", $"{_baseUrl}/auth/check");
                return false;
            }
        }
    }

    public class LoginResponse
    {
        public bool Success { get; set; }
        public UserInfo? User { get; set; }
        public string? Message { get; set; }
    }

    public class UserInfo
    {
        public string? LoginName { get; set; }
        public string? UserName { get; set; }
        public string? RoleName { get; set; }
        public int RoleId { get; set; }
        public int AccessLevel { get; set; }
        public bool IsAuthenticated { get; set; }
    }

    public class AuthenticationCheck
    {
        public bool IsAuthenticated { get; set; }
        public string? UserName { get; set; }
    }
}
