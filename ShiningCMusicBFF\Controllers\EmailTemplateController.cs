using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShiningCMusicBFF.Services;
using ShiningCMusicCommon.Models;
using System.Text.Json;

namespace ShiningCMusicBFF.Controllers
{
    [ApiController]
    [Route("bff/email-templates")]
    [Authorize]
    public class EmailTemplateController : ControllerBase
    {
        private readonly ApiClientService _apiClient;
        private readonly ILogger<EmailTemplateController> _logger;

        public EmailTemplateController(ApiClientService apiClient, ILogger<EmailTemplateController> logger)
        {
            _apiClient = apiClient;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> GetTemplates()
        {
            try
            {
                _logger.LogInformation("Loading email templates from API");
                
                var templates = await _apiClient.GetJsonAsync<List<EmailTemplate>>("emailtemplates");
                return Ok(templates ?? new List<EmailTemplate>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading email templates from API");
                return StatusCode(500, new { message = "An error occurred loading email templates" });
            }
        }

        [HttpGet("{templateName}")]
        public async Task<IActionResult> GetTemplate(string templateName)
        {
            try
            {
                _logger.LogInformation("Loading email template from API: {TemplateName}", templateName);
                
                var response = await _apiClient.GetAsync($"emailtemplates/{templateName}");
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var template = JsonSerializer.Deserialize<EmailTemplate>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    return Ok(template);
                }
                
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "Email template not found" });
                }
                
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to load email template. Status: {StatusCode}, Content: {Content}", 
                    response.StatusCode, errorContent);
                return StatusCode(500, new { message = "An error occurred loading email template" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading email template {TemplateName} from API", templateName);
                return StatusCode(500, new { message = "An error occurred loading email template" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> CreateTemplate([FromBody] EmailTemplate template)
        {
            try
            {
                _logger.LogInformation("Creating email template via API: {TemplateName}", template.Name);
                
                var response = await _apiClient.PostAsync("emailtemplates", template);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var createdTemplate = JsonSerializer.Deserialize<EmailTemplate>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    
                    return Ok(new { success = true, message = "Email template created successfully", template = createdTemplate });
                }
                
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to create email template. Status: {StatusCode}, Content: {Content}", 
                    response.StatusCode, errorContent);
                return BadRequest(new { message = "Failed to create email template" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating email template via API");
                return StatusCode(500, new { message = "An error occurred creating email template" });
            }
        }

        [HttpPut("{templateName}")]
        public async Task<IActionResult> UpdateTemplate(string templateName, [FromBody] EmailTemplate template)
        {
            try
            {
                _logger.LogInformation("Updating email template via API: {TemplateName}", templateName);
                
                template.Name = templateName;
                var response = await _apiClient.PutAsync($"emailtemplates/{templateName}", template);
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { success = true, message = "Email template updated successfully" });
                }
                
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "Email template not found" });
                }
                
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to update email template. Status: {StatusCode}, Content: {Content}", 
                    response.StatusCode, errorContent);
                return BadRequest(new { message = "Failed to update email template" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating email template {TemplateName} via API", templateName);
                return StatusCode(500, new { message = "An error occurred updating email template" });
            }
        }

        [HttpDelete("{templateName}")]
        public async Task<IActionResult> DeleteTemplate(string templateName)
        {
            try
            {
                _logger.LogInformation("Deleting email template via API: {TemplateName}", templateName);
                
                var response = await _apiClient.DeleteAsync($"emailtemplates/{templateName}");
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { success = true, message = "Email template deleted successfully" });
                }
                
                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "Email template not found" });
                }
                
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to delete email template. Status: {StatusCode}, Content: {Content}", 
                    response.StatusCode, errorContent);
                return BadRequest(new { message = "Failed to delete email template" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting email template {TemplateName} via API", templateName);
                return StatusCode(500, new { message = "An error occurred deleting email template" });
            }
        }

        [HttpPost("preview")]
        public async Task<IActionResult> PreviewTemplate([FromBody] EmailTemplatePreviewRequest request)
        {
            try
            {
                _logger.LogInformation("Previewing email template via API: {TemplateName}", request.TemplateName);
                
                // Note: The original API doesn't have a preview endpoint, so this would need to be implemented
                // For now, we'll return a simple preview based on the template content
                var templateResponse = await _apiClient.GetAsync($"emailtemplates/{request.TemplateName}");
                if (!templateResponse.IsSuccessStatusCode)
                {
                    return NotFound(new { message = "Email template not found" });
                }
                
                var templateContent = await templateResponse.Content.ReadAsStringAsync();
                var template = JsonSerializer.Deserialize<EmailTemplate>(templateContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
                
                // Simple preview - replace placeholders with sample data
                var preview = template?.BodyHtml ?? template?.BodyText ?? "";
                foreach (var kvp in request.SampleData)
                {
                    preview = preview.Replace($"{{{kvp.Key}}}", kvp.Value);
                }
                
                return Ok(new { preview });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error previewing email template {TemplateName} via API", request.TemplateName);
                return StatusCode(500, new { message = "An error occurred previewing email template" });
            }
        }

        // Attachment Management Endpoints

        [HttpGet("{templateName}/attachments")]
        public async Task<IActionResult> GetTemplateAttachments(string templateName)
        {
            try
            {
                _logger.LogInformation("Loading attachments for template via API: {TemplateName}", templateName);

                var attachments = await _apiClient.GetJsonAsync<List<EmailAttachment>>($"emailtemplates/{templateName}/attachments");
                return Ok(attachments ?? new List<EmailAttachment>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading attachments for template {TemplateName} from API", templateName);
                return StatusCode(500, new { message = "An error occurred loading template attachments" });
            }
        }

        [HttpPost("attachments")]
        public async Task<IActionResult> AddAttachment([FromBody] EmailAttachment attachment)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(attachment.TemplateName) ||
                    string.IsNullOrWhiteSpace(attachment.AttachmentName) ||
                    string.IsNullOrWhiteSpace(attachment.AttachmentPath))
                {
                    return BadRequest(new { message = "Template name, attachment name, and attachment path are required" });
                }

                _logger.LogInformation("Adding attachment to template via API: {TemplateName}", attachment.TemplateName);

                var response = await _apiClient.PostAsync("emailtemplates/attachments", attachment);
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var createdAttachment = JsonSerializer.Deserialize<EmailAttachment>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    return Ok(new { success = true, message = "Attachment added successfully", attachment = createdAttachment });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to add attachment. Status: {StatusCode}, Content: {Content}",
                    response.StatusCode, errorContent);
                return BadRequest(new { message = "Failed to add attachment" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding attachment to template {TemplateName} via API", attachment.TemplateName);
                return StatusCode(500, new { message = "An error occurred adding attachment" });
            }
        }

        [HttpDelete("attachments/{attachmentId}")]
        public async Task<IActionResult> DeleteAttachment(int attachmentId)
        {
            try
            {
                _logger.LogInformation("Deleting attachment via API: {AttachmentId}", attachmentId);

                var response = await _apiClient.DeleteAsync($"emailtemplates/attachments/{attachmentId}");
                if (response.IsSuccessStatusCode)
                {
                    return Ok(new { success = true, message = "Attachment deleted successfully" });
                }

                if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                {
                    return NotFound(new { message = "Attachment not found" });
                }

                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogWarning("Failed to delete attachment. Status: {StatusCode}, Content: {Content}",
                    response.StatusCode, errorContent);
                return BadRequest(new { message = "Failed to delete attachment" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting attachment {AttachmentId} via API", attachmentId);
                return StatusCode(500, new { message = "An error occurred deleting attachment" });
            }
        }
    }

    public class EmailTemplatePreviewRequest
    {
        public string TemplateName { get; set; } = string.Empty;
        public Dictionary<string, string> SampleData { get; set; } = new();
    }
}
