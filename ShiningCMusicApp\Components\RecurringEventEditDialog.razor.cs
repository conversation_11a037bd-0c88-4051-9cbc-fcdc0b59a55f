using Microsoft.AspNetCore.Components;

namespace ShiningCMusicApp.Components
{
    public partial class RecurringEventEditDialog : ComponentBase
    {
        private Syncfusion.Blazor.Popups.SfDialog? dialogRef;
        private bool isVisible = false;
        private EditOption selectedOption = EditOption.EditEvent;
        private TaskCompletionSource<EditOption?>? taskCompletionSource;

        public enum EditOption
        {
            EditEvent,
            EditFollowingEvents,
            EditSeries
        }

        public async Task<EditOption?> ShowAsync()
        {
            selectedOption = EditOption.EditEvent; // Default selection
            isVisible = true;
            taskCompletionSource = new TaskCompletionSource<EditOption?>();
            StateHasChanged();
            
            return await taskCompletionSource.Task;
        }

        private void OnConfirm()
        {
            isVisible = false;
            taskCompletionSource?.SetResult(selectedOption);
            StateHasChanged();
        }

        private void OnCancel()
        {
            isVisible = false;
            taskCompletionSource?.SetResult(null);
            StateHasChanged();
        }
    }
}
