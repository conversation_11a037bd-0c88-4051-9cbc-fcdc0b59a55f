@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.Buttons
@namespace ShiningCMusicApp.Components

<SfDialog @ref="dialogRef"
          Width="400px"
          Height="auto"
          IsModal="true"
          ShowCloseIcon="false"
          Visible="@isVisible"
          CssClass="recurring-event-edit-dialog">
    <DialogTemplates>
        <Header>
            <div class="dialog-header">
                <i class="bi bi-arrow-repeat me-2"></i>
                <span>Edit Recurring Event</span>
            </div>
        </Header>
        <Content>
            <div class="dialog-content">
                <p class="mb-3">This is a recurring event. How would you like to edit it?</p>

                <div class="edit-options">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="radio" name="editOption" id="editEvent"
                               @onchange="() => selectedOption = EditOption.EditEvent"
                               checked="@(selectedOption == EditOption.EditEvent)">
                        <label class="form-check-label" for="editEvent">
                            <strong>Edit Event</strong>
                            <small class="d-block text-muted">Edit only this occurrence</small>
                        </label>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="radio" name="editOption" id="editFollowing"
                               @onchange="() => selectedOption = EditOption.EditFollowingEvents"
                               checked="@(selectedOption == EditOption.EditFollowingEvents)">
                        <label class="form-check-label" for="editFollowing">
                            <strong>Edit Following Events</strong>
                            <small class="d-block text-muted">Edit this and all future occurrences</small>
                        </label>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="radio" name="editOption" id="editSeries"
                               @onchange="() => selectedOption = EditOption.EditSeries"
                               checked="@(selectedOption == EditOption.EditSeries)">
                        <label class="form-check-label" for="editSeries">
                            <strong>Edit Series</strong>
                            <small class="d-block text-muted">Edit the entire recurring series</small>
                        </label>
                    </div>
                </div>
            </div>
        </Content>
        <FooterTemplate>
            <div class="dialog-footer">
                <SfButton CssClass="btn btn-blue-custom" @onclick="OnConfirm">
                    <i class="bi bi-check-circle" style="color: white;"></i><span class="ms-2">OK</span>
                </SfButton>
                <SfButton CssClass="btn btn-cancel-custom" @onclick="OnCancel">
                    <i class="bi bi-x-circle" style="color: inherit;"></i><span class="ms-2">Cancel</span>
                </SfButton>
            </div>
        </FooterTemplate>
    </DialogTemplates>
</SfDialog>
