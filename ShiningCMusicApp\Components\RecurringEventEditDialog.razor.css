.recurring-event-edit-dialog .dialog-header {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.recurring-event-edit-dialog .dialog-content {
    padding: 0;
}

.recurring-event-edit-dialog .edit-options {
    margin-left: 10px;
}

.recurring-event-edit-dialog .form-check {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    margin-bottom: 0.75rem;
    padding: 0 12px;
    border: none;
}

.recurring-event-edit-dialog .form-check:hover {
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

.recurring-event-edit-dialog .form-check-input {
    margin-top: 0.125rem;
    width: 1.2em;
    height: 1.2em;
}

.recurring-event-edit-dialog .form-check-label {
    cursor: pointer;
    line-height: 1.4;
    margin-left: 8px;
}

.recurring-event-edit-dialog .form-check-label strong {
    display: block;
    margin-bottom: 2px;
    color: #333;
}

.recurring-event-edit-dialog .form-check-label small {
    font-size: 0.875rem;
    color: #6c757d;
    line-height: 1.3;
}

.recurring-event-edit-dialog .dialog-footer {
    margin-top: 0;
    border-top: none;
    padding-top: 0;
}

.recurring-event-edit-dialog .e-dialog {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.recurring-event-edit-dialog .e-dialog .e-dlg-content {
    padding: 20px 24px;
}

.recurring-event-edit-dialog .e-dialog .e-dlg-header-content {
    padding: 16px 24px;
    border-bottom: 1px solid #dee2e6;
}

@media (max-width: 768px) {
    .recurring-event-edit-dialog .dialog-footer {
        display: flex;
        flex-direction: column !important;
        gap: 0.5rem !important; /* space between buttons */
    }
}
