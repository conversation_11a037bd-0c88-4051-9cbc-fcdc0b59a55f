using System;
using ShiningCMusicCommon.Enums;

namespace ShiningCMusicCommon.Extensions
{
    public static class BackgroundProcessorExtensions
    {
        public static string GetDisplayName(this BackgroundProcessor processor)
        {
            return processor switch
            {
                BackgroundProcessor.LessonCleanup => BackgroundProcessorKeys.LessonCleanupDisplay,
                BackgroundProcessor.TutorCleanup => BackgroundProcessorKeys.TutorCleanupDisplay,
                BackgroundProcessor.StudentCleanup => BackgroundProcessorKeys.StudentCleanupDisplay,
                BackgroundProcessor.PaymentReminder => BackgroundProcessorKeys.PaymentReminderDisplay,
                _ => processor.ToString()
            };
        }

        public static string GetDescription(this BackgroundProcessor processor)
        {
            return processor switch
            {
                BackgroundProcessor.LessonCleanup => BackgroundProcessorKeys.LessonCleanupDescription,
                BackgroundProcessor.TutorCleanup => BackgroundProcessorKeys.TutorCleanupDescription,
                BackgroundProcessor.StudentCleanup => BackgroundProcessorKeys.StudentCleanupDescription,
                BackgroundProcessor.PaymentReminder => BackgroundProcessorKeys.PaymentReminderDescription,
                _ => string.Empty
            };
        }

        public static string ToConfigKey(this BackgroundProcessor processor)
        {
            return processor switch
            {
                BackgroundProcessor.LessonCleanup => BackgroundProcessorKeys.LessonCleanup,
                BackgroundProcessor.TutorCleanup => BackgroundProcessorKeys.TutorCleanup,
                BackgroundProcessor.StudentCleanup => BackgroundProcessorKeys.StudentCleanup,
                BackgroundProcessor.PaymentReminder => BackgroundProcessorKeys.PaymentReminder,
                _ => processor.ToString()
            };
        }

        public static string ToEnabledConfigKey(this BackgroundProcessor processor)
        {
            return processor switch
            {
                BackgroundProcessor.LessonCleanup => BackgroundProcessorKeys.LessonCleanupEnabled,
                BackgroundProcessor.TutorCleanup => BackgroundProcessorKeys.TutorCleanupEnabled,
                BackgroundProcessor.StudentCleanup => BackgroundProcessorKeys.StudentCleanupEnabled,
                BackgroundProcessor.PaymentReminder => BackgroundProcessorKeys.PaymentReminderEnabled,
                _ => $"{processor}Enabled"
            };
        }
    }
}
