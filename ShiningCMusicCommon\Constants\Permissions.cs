namespace ShiningCMusicCommon.Constants
{
    /// <summary>
    /// Centralized permission constants for access level requirements
    /// </summary>
    public static class Permissions
    {
        // Pure numeric access levels (no role names)
        public const int Level10 = 10;  // Student level access
        public const int Level20 = 20;  // Tutor level access
        public const int Level80 = 80;  // Manager level access
        public const int Level100 = 100; // Administrator level access

        // Feature-specific permissions
        public static class Lessons
        {
            public const int View = Level10;    // Level 10+ can view lessons
            public const int Edit = Level80;    // Level 80+ can edit all lessons
            public const int Create = Level80;  // Level 80+ can create lessons
            public const int Delete = Level100; // Level 100 can delete lessons
        }

        public static class Timesheets
        {
            public const int View = Level20;    // Level 20+ can view timesheets
            public const int Create = Level20;  // Level 20+ can create timesheets
            public const int Edit = Level80;    // Level 80+ can edit all timesheets
            public const int Delete = Level100; // Level 100 can delete timesheets
        }

        public static class Users
        {
            public const int View = Level80;    // Level 80+ can view users
            public const int Create = Level80;  // Level 80+ can create users
            public const int Edit = Level80;    // Level 80+ can edit users
            public const int Delete = Level100; // Level 100 can delete users
        }

        public static class Tutors
        {
            public const int View = Level80;    // Level 80+ can view tutors
            public const int Create = Level80;  // Level 80+ can create tutors
            public const int Edit = Level80;    // Level 80+ can edit tutors
            public const int Delete = Level100; // Level 100 can delete tutors
        }

        public static class Students
        {
            public const int View = Level80;    // Level 80+ can view students
            public const int Create = Level80;  // Level 80+ can create students
            public const int Edit = Level80;    // Level 80+ can edit students
            public const int Delete = Level100; // Level 100 can delete students
        }

        public static class EmailTemplates
        {
            public const int View = Level80;    // Level 80+ can view templates
            public const int Create = Level80;  // Level 80+ can create templates
            public const int Edit = Level80;    // Level 80+ can edit templates
            public const int Delete = Level100; // Level 100 can delete templates
        }

        public static class System
        {
            public const int ViewSettings = Level80;      // Level 80+ can view settings
            public const int EditSettings = Level100;     // Level 100 can edit settings
            public const int ViewMaintenance = Level80;   // Level 80+ can view maintenance
            public const int EditMaintenance = Level100;  // Level 100 can edit maintenance
        }

        // Helper methods for common permission checks
        public static class Check
        {
            public static bool CanViewLessons(int accessLevel) => accessLevel >= Lessons.View;
            public static bool CanEditLessons(int accessLevel) => accessLevel >= Lessons.Edit;
            public static bool CanCreateLessons(int accessLevel) => accessLevel >= Lessons.Create;
            public static bool CanDeleteLessons(int accessLevel) => accessLevel >= Lessons.Delete;

            public static bool CanViewTimesheets(int accessLevel) => accessLevel >= Timesheets.View;
            public static bool CanEditTimesheets(int accessLevel) => accessLevel >= Timesheets.Edit;
            public static bool CanCreateTimesheets(int accessLevel) => accessLevel >= Timesheets.Create;
            public static bool CanDeleteTimesheets(int accessLevel) => accessLevel >= Timesheets.Delete;

            public static bool CanManageUsers(int accessLevel) => accessLevel >= Users.View;
            public static bool CanManageTutors(int accessLevel) => accessLevel >= Tutors.View;
            public static bool CanManageStudents(int accessLevel) => accessLevel >= Students.View;
            public static bool CanManageEmailTemplates(int accessLevel) => accessLevel >= EmailTemplates.View;
            
            public static bool CanViewSettings(int accessLevel) => accessLevel >= System.ViewSettings;
            public static bool CanEditSettings(int accessLevel) => accessLevel >= System.EditSettings;
            public static bool CanViewMaintenance(int accessLevel) => accessLevel >= System.ViewMaintenance;
            public static bool CanEditMaintenance(int accessLevel) => accessLevel >= System.EditMaintenance;
        }
    }
}
