using System.ComponentModel;

namespace ShiningCMusicCommon.Enums
{
    public static class SidebarThemeColors
    {
        // CSS Gradients (180deg - for sidebar)
        public const string BluePurpleGradient = "linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%)";
        public const string BlueGrayGradient = "linear-gradient(180deg, #2c3e50 0%, #34495e 70%)";
        public const string PurpleGradient = "linear-gradient(180deg, #8e44ad 0%, #9b59b6 70%)";
        public const string DarkGrayGradient = "linear-gradient(180deg, #34495e 0%, #2c3e50 70%)";
        public const string NavyBlueGradient = "linear-gradient(180deg, #2c3e50 0%, #1a252f 70%)";
        public const string TealGradient = "linear-gradient(180deg, #16a085 0%, #1abc9c 70%)";
        public const string DarkBlueGradient = "linear-gradient(180deg, #1a252f 0%, #2c3e50 70%)";
        public const string GoldAmberGradient = "linear-gradient(180deg, #b8860b 0%, #ff8c00 70%)";
        public const string SilverCharcoalGradient = "linear-gradient(180deg, #708090 0%, #2f4f4f 70%)";
        public const string DeepPurpleIndigoGradient = "linear-gradient(180deg, #483d8b 0%, #4b0082 70%)";
        public const string MidnightSteelGradient = "linear-gradient(180deg, #191970 0%, #4682b4 70%)";
        public const string ElegantBlackGrayGradient = "linear-gradient(180deg, #2c2c2c 0%, #1a1a1a 70%)";
        public const string DefaultGradient = BluePurpleGradient;

        // Preview Gradients (90deg - for preview swatches)
        public const string BluePurplePreview = "linear-gradient(90deg, rgb(5, 39, 103) 0%, #3a0647 100%)";
        public const string BlueGrayPreview = "linear-gradient(90deg, #2c3e50 0%, #34495e 100%)";
        public const string PurplePreview = "linear-gradient(90deg, #8e44ad 0%, #9b59b6 100%)";
        public const string DarkGrayPreview = "linear-gradient(90deg, #34495e 0%, #2c3e50 100%)";
        public const string NavyBluePreview = "linear-gradient(90deg, #2c3e50 0%, #1a252f 100%)";
        public const string TealPreview = "linear-gradient(90deg, #16a085 0%, #1abc9c 100%)";
        public const string DarkBluePreview = "linear-gradient(90deg, #1a252f 0%, #2c3e50 100%)";
        public const string GoldAmberPreview = "linear-gradient(90deg, #b8860b 0%, #ff8c00 100%)";
        public const string SilverCharcoalPreview = "linear-gradient(90deg, #708090 0%, #2f4f4f 100%)";
        public const string DeepPurpleIndigoPreview = "linear-gradient(90deg, #483d8b 0%, #4b0082 100%)";
        public const string MidnightSteelPreview = "linear-gradient(90deg, #191970 0%, #4682b4 100%)";
        public const string ElegantBlackGrayPreview = "linear-gradient(90deg, #2c2c2c 0%, #1a1a1a 100%)";
        public const string CustomPreview = "linear-gradient(90deg, #6c5ce7 0%, #a29bfe 100%)";
        public const string DefaultPreview = BluePurplePreview;
    }
    public enum SidebarTheme
    {
        [Description("Blue to Purple (Default)")]
        BluePurple = 1,

        [Description("Blue Gray")]
        BlueGray = 2,

        [Description("Purple Gradient")]
        Purple = 3,

        [Description("Dark Gray")]
        DarkGray = 4,

        [Description("Navy Blue")]
        NavyBlue = 5,

        [Description("Teal Gradient")]
        Teal = 6,

        [Description("Dark Blue")]
        DarkBlue = 7,

        [Description("Gold to Amber")]
        GoldAmber = 8,

        [Description("Silver to Charcoal")]
        SilverCharcoal = 9,

        [Description("Deep Purple to Indigo")]
        DeepPurpleIndigo = 10,

        [Description("Midnight Blue to Steel")]
        MidnightSteel = 11,

        [Description("Elegant Black to Gray")]
        ElegantBlackGray = 12,

        [Description("Custom Colors")]
        Custom = 13
    }
    
    public static class SidebarThemeExtensions
    {
        public static string GetCssGradient(this SidebarTheme theme)
        {
            return theme switch
            {
                SidebarTheme.BluePurple => SidebarThemeColors.BluePurpleGradient,
                SidebarTheme.BlueGray => SidebarThemeColors.BlueGrayGradient,
                SidebarTheme.Purple => SidebarThemeColors.PurpleGradient,
                SidebarTheme.DarkGray => SidebarThemeColors.DarkGrayGradient,
                SidebarTheme.NavyBlue => SidebarThemeColors.NavyBlueGradient,
                SidebarTheme.Teal => SidebarThemeColors.TealGradient,
                SidebarTheme.DarkBlue => SidebarThemeColors.DarkBlueGradient,
                SidebarTheme.GoldAmber => SidebarThemeColors.GoldAmberGradient,
                SidebarTheme.SilverCharcoal => SidebarThemeColors.SilverCharcoalGradient,
                SidebarTheme.DeepPurpleIndigo => SidebarThemeColors.DeepPurpleIndigoGradient,
                SidebarTheme.MidnightSteel => SidebarThemeColors.MidnightSteelGradient,
                SidebarTheme.ElegantBlackGray => SidebarThemeColors.ElegantBlackGrayGradient,
                SidebarTheme.Custom => SidebarThemeColors.DefaultGradient, // Default, will be overridden
                _ => SidebarThemeColors.DefaultGradient
            };
        }
        
        public static string GetPreviewColor(this SidebarTheme theme)
        {
            return theme switch
            {
                SidebarTheme.BluePurple => SidebarThemeColors.BluePurplePreview,
                SidebarTheme.BlueGray => SidebarThemeColors.BlueGrayPreview,
                SidebarTheme.Purple => SidebarThemeColors.PurplePreview,
                SidebarTheme.DarkGray => SidebarThemeColors.DarkGrayPreview,
                SidebarTheme.NavyBlue => SidebarThemeColors.NavyBluePreview,
                SidebarTheme.Teal => SidebarThemeColors.TealPreview,
                SidebarTheme.DarkBlue => SidebarThemeColors.DarkBluePreview,
                SidebarTheme.GoldAmber => SidebarThemeColors.GoldAmberPreview,
                SidebarTheme.SilverCharcoal => SidebarThemeColors.SilverCharcoalPreview,
                SidebarTheme.DeepPurpleIndigo => SidebarThemeColors.DeepPurpleIndigoPreview,
                SidebarTheme.MidnightSteel => SidebarThemeColors.MidnightSteelPreview,
                SidebarTheme.ElegantBlackGray => SidebarThemeColors.ElegantBlackGrayPreview,
                SidebarTheme.Custom => SidebarThemeColors.CustomPreview,
                _ => SidebarThemeColors.DefaultPreview
            };
        }
        
        public static string GetDescription(this SidebarTheme theme)
        {
            var field = theme.GetType().GetField(theme.ToString());
            var attribute = field?.GetCustomAttributes(typeof(DescriptionAttribute), false)
                .FirstOrDefault() as DescriptionAttribute;
            return attribute?.Description ?? theme.ToString();
        }
    }
}
