namespace ShiningCMusicCommon.Enums
{
    public static class BackgroundProcessorKeys
    {
        // Configuration Keys
        public const string LessonCleanup = "LessonCleanup";
        public const string TutorCleanup = "TutorCleanup";
        public const string StudentCleanup = "StudentCleanup";
        public const string PaymentReminder = "PaymentReminder";

        public const string LessonCleanupEnabled = "LessonCleanupEnabled";
        public const string TutorCleanupEnabled = "TutorCleanupEnabled";
        public const string StudentCleanupEnabled = "StudentCleanupEnabled";
        public const string PaymentReminderEnabled = "PaymentReminderEnabled";

        // Display Names
        public const string LessonCleanupDisplay = "Lesson Cleanup";
        public const string TutorCleanupDisplay = "Tutor Cleanup";
        public const string StudentCleanupDisplay = "Student Cleanup";
        public const string PaymentReminderDisplay = "Payment Reminders";

        // Descriptions
        public const string LessonCleanupDescription = "Automatically removes old archived lessons";
        public const string TutorCleanupDescription = "Automatically removes old archived tutors";
        public const string StudentCleanupDescription = "Automatically removes old archived students";
        public const string PaymentReminderDescription = "Sends payment reminder emails to students";
    }

    public enum BackgroundProcessor
    {
        LessonCleanup,
        TutorCleanup,
        StudentCleanup,
        PaymentReminder
    }
}
