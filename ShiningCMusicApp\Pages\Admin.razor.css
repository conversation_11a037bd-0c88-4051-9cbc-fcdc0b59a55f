/* Admin page specific styles */

.password-validation {
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.password-validation .text-danger {
    color: #dc3545 !important;
}

.password-validation .text-success {
    color: #198754 !important;
}

.password-validation small {
    display: block;
    margin-bottom: 0.125rem;
}

.password-validation small:last-child {
    margin-bottom: 0;
}

/* Section header icon alignment */
.card-header h5 {
    flex-wrap: nowrap;
}

.card-header h5 span {
    display: inline-block;
    vertical-align: middle;
}

/* Ensure User Roles text stays together */
.card-header h5 span:not(:first-child) {
    white-space: nowrap;
}

/* Specific fix for User Roles section */
.card-header h5 {
    min-width: fit-content;
}

/* Grid version for user roles table */
.access-level-icon-grid {
    position: relative;
    display: inline-block;
}

.access-level-svg-grid {
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.15));
}

.access-level-text-grid {
    font-size: 12px;
    font-weight: bold;
    fill: #333;
}
