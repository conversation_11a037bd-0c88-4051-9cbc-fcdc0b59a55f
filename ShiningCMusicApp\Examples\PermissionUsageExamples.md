# Permission Management System Usage Examples

## 🎯 **New Permission System Benefits:**

1. **Centralized** - All permissions defined in one place
2. **Readable** - `user.CanEditLessons()` vs `user.HasAccessLevel(80)`
3. **Maintainable** - Change permission levels without touching UI code
4. **Flexible** - Easy to add new permissions or modify existing ones

## 📋 **Page-Level Authorization Examples:**

### **Old Way:**
```csharp
@attribute [RequireAccessLevel(80)]
```

### **New Way:**
```csharp
@attribute [RequireManagerAccess]
// or
@attribute [RequireAccessLevel(Permissions.Manager)]
```

## 🔧 **UI Element Authorization Examples:**

### **Old Way:**
```razor
@if (authState?.User?.HasAccessLevel(80) == true)
{
    <button>Edit Lesson</button>
}
```

### **New Way:**
```razor
@if (authState?.User?.CanEditLessons() == true)
{
    <button>Edit Lesson</button>
}
```

## 🎨 **Component Examples:**

### **Lessons Page:**
```razor
@page "/lessons"
@attribute [RequireStudentAccess]  <!-- Students can view lessons -->

<!-- View lessons - all authenticated users -->
@if (user.CanViewLessons())
{
    <div>Lesson calendar here</div>
}

<!-- Edit/Create buttons - managers only -->
@if (user.CanEditLessons())
{
    <button @onclick="CreateLesson">Create Lesson</button>
    <button @onclick="EditLesson">Edit Lesson</button>
}

<!-- Delete button - admins only -->
@if (user.CanDeleteLessons())
{
    <button @onclick="DeleteLesson" class="btn-danger">Delete Lesson</button>
}
```

### **Timesheets Page:**
```razor
@page "/timesheets"
@attribute [RequireTutorAccess]  <!-- Tutors can access timesheets -->

@if (user.CanViewTimesheets())
{
    <div>Timesheet grid here</div>
}

@if (user.CanCreateTimesheets())
{
    <button @onclick="CreateTimesheet">Create Timesheet</button>
}

@if (user.CanEditTimesheets())
{
    <button @onclick="EditTimesheet">Edit Timesheet</button>
}

@if (user.CanDeleteTimesheets())
{
    <button @onclick="DeleteTimesheet" class="btn-danger">Delete</button>
}
```

### **Admin Pages:**
```razor
@page "/admin"
@attribute [RequireManagerAccess]  <!-- Managers can access admin -->

@if (user.CanViewMaintenance())
{
    <div>Maintenance panel here</div>
}

@if (user.CanEditMaintenance())
{
    <button @onclick="EditSettings">Edit Settings</button>
}
```

## 🔄 **Easy Migration Steps:**

### **Step 1: Update Page Attributes**
```csharp
// Find & Replace:
[RequireAccessLevel(100)] → [RequireAdminAccess]
[RequireAccessLevel(80)]  → [RequireManagerAccess]
[RequireAccessLevel(20)]  → [RequireTutorAccess]
[RequireAccessLevel(10)]  → [RequireStudentAccess]
```

### **Step 2: Update UI Checks**
```csharp
// Find & Replace:
user.HasAccessLevel(100) → user.CanEditSettings()
user.HasAccessLevel(80)  → user.CanEditLessons()
user.HasAccessLevel(20)  → user.CanViewTimesheets()
user.HasAccessLevel(10)  → user.CanViewLessons()
```

### **Step 3: Add Using Statement**
```csharp
@using ShiningCMusicCommon.Constants
```

## 🚀 **Advanced Features:**

### **Custom Permission Checks:**
```csharp
// In Permissions.cs, add new methods:
public static bool CanExportData(int accessLevel) => accessLevel >= Manager;
public static bool CanImportData(int accessLevel) => accessLevel >= Administrator;

// In ClaimsPrincipalExtensions.cs:
public static bool CanExportData(this ClaimsPrincipal user) => 
    Permissions.Check.CanExportData(user.GetAccessLevel());
```

### **Feature Flags:**
```csharp
public static class Features
{
    public const bool EnableAdvancedReporting = true;
    public const bool EnableBulkOperations = false;
}

// Usage:
@if (Features.EnableAdvancedReporting && user.CanViewReports())
{
    <button>Advanced Reports</button>
}
```

## 📊 **Permission Matrix:**

| Feature | Student (10) | Tutor (20) | Manager (80) | Admin (100) |
|---------|-------------|------------|--------------|-------------|
| View Lessons | ✅ | ✅ | ✅ | ✅ |
| Edit Lessons | ❌ | ❌ | ✅ | ✅ |
| View Timesheets | ❌ | ✅ | ✅ | ✅ |
| Edit Timesheets | ❌ | ❌ | ✅ | ✅ |
| Manage Users | ❌ | ❌ | ✅ | ✅ |
| System Settings | ❌ | ❌ | ❌ | ✅ |

This system makes permissions much more maintainable and readable!
