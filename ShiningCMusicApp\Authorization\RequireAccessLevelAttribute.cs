using Microsoft.AspNetCore.Authorization;
using ShiningCMusicCommon.Constants;

namespace ShiningCMusicApp.Authorization
{
    public class RequireAccessLevelAttribute : AuthorizeAttribute
    {
        public RequireAccessLevelAttribute(int minimumAccessLevel)
        {
            Policy = $"AccessLevel{minimumAccessLevel}";
        }
    }

    // Convenience attributes for common access levels (using numeric levels)
    public class RequireLevel10AccessAttribute : RequireAccessLevelAttribute
    {
        public RequireLevel10AccessAttribute() : base(Permissions.Level10) { }
    }

    public class RequireLevel20AccessAttribute : RequireAccessLevelAttribute
    {
        public RequireLevel20AccessAttribute() : base(Permissions.Level20) { }
    }

    public class RequireLevel80AccessAttribute : RequireAccessLevelAttribute
    {
        public RequireLevel80AccessAttribute() : base(Permissions.Level80) { }
    }

    public class RequireLevel100AccessAttribute : RequireAccessLevelAttribute
    {
        public RequireLevel100AccessAttribute() : base(Permissions.Level100) { }
    }
}
