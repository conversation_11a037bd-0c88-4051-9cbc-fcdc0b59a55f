using System.Security.Claims;
using ShiningCMusicCommon.Enums;
using ShiningCMusicCommon.Constants;

namespace ShiningCMusicCommon.Extensions
{
    /// <summary>
    /// Extension methods for ClaimsPrincipal to provide type-safe role checking
    /// </summary>
    public static class ClaimsPrincipalExtensions
    {
        /// <summary>
        /// Checks if the user is in the specified role using the UserRoleEnum
        /// </summary>
        /// <param name="user">The ClaimsPrincipal user</param>
        /// <param name="role">The role to check</param>
        /// <returns>True if the user is in the specified role</returns>
        public static bool IsInRole(this ClaimsPrincipal user, UserRoleEnum role)
        {
            return user.IsInRole(role.ToString());
        }

        /// <summary>
        /// Gets the user's role as a UserRoleEnum
        /// </summary>
        /// <param name="user">The ClaimsPrincipal user</param>
        /// <returns>The user's role enum, or null if not found or invalid</returns>
        public static UserRoleEnum? GetUserRole(this ClaimsPrincipal user)
        {
            var roleIdClaim = user.FindFirst("RoleId")?.Value;
            if (int.TryParse(roleIdClaim, out int roleId) && 
                Enum.IsDefined(typeof(UserRoleEnum), roleId))
            {
                return (UserRoleEnum)roleId;
            }
            return null;
        }

        /// <summary>
        /// Gets the user's role ID as an integer
        /// </summary>
        /// <param name="user">The ClaimsPrincipal user</param>
        /// <returns>The user's role ID, or null if not found</returns>
        public static int? GetUserRoleId(this ClaimsPrincipal user)
        {
            var roleIdClaim = user.FindFirst("RoleId")?.Value;
            if (int.TryParse(roleIdClaim, out int roleId))
            {
                return roleId;
            }
            return null;
        }

        /// <summary>
        /// Gets the user's access level from claims
        /// </summary>
        /// <param name="user">The ClaimsPrincipal user</param>
        /// <returns>The user's access level, or 0 if not found</returns>
        public static int GetAccessLevel(this ClaimsPrincipal user)
        {
            var accessLevelClaim = user.FindFirst("AccessLevel")?.Value;
            if (int.TryParse(accessLevelClaim, out int accessLevel))
            {
                return accessLevel;
            }
            return 0;
        }

        /// <summary>
        /// Checks if the user has at least the specified access level
        /// </summary>
        /// <param name="user">The ClaimsPrincipal user</param>
        /// <param name="requiredLevel">The minimum required access level</param>
        /// <returns>True if the user has sufficient access level</returns>
        public static bool HasAccessLevel(this ClaimsPrincipal user, int requiredLevel)
        {
            return user.GetAccessLevel() >= requiredLevel;
        }

        // Permission-based methods using the Permissions constants

        // Lessons permissions
        public static bool CanViewLessons(this ClaimsPrincipal user) => Permissions.Check.CanViewLessons(user.GetAccessLevel());
        public static bool CanEditLessons(this ClaimsPrincipal user) => Permissions.Check.CanEditLessons(user.GetAccessLevel());
        public static bool CanCreateLessons(this ClaimsPrincipal user) => Permissions.Check.CanCreateLessons(user.GetAccessLevel());
        public static bool CanDeleteLessons(this ClaimsPrincipal user) => Permissions.Check.CanDeleteLessons(user.GetAccessLevel());

        // Timesheets permissions
        public static bool CanViewTimesheets(this ClaimsPrincipal user) => Permissions.Check.CanViewTimesheets(user.GetAccessLevel());
        public static bool CanEditTimesheets(this ClaimsPrincipal user) => Permissions.Check.CanEditTimesheets(user.GetAccessLevel());
        public static bool CanCreateTimesheets(this ClaimsPrincipal user) => Permissions.Check.CanCreateTimesheets(user.GetAccessLevel());
        public static bool CanDeleteTimesheets(this ClaimsPrincipal user) => Permissions.Check.CanDeleteTimesheets(user.GetAccessLevel());

        // Management permissions
        public static bool CanManageUsers(this ClaimsPrincipal user) => Permissions.Check.CanManageUsers(user.GetAccessLevel());
        public static bool CanManageTutors(this ClaimsPrincipal user) => Permissions.Check.CanManageTutors(user.GetAccessLevel());
        public static bool CanManageStudents(this ClaimsPrincipal user) => Permissions.Check.CanManageStudents(user.GetAccessLevel());
        public static bool CanManageEmailTemplates(this ClaimsPrincipal user) => Permissions.Check.CanManageEmailTemplates(user.GetAccessLevel());

        // System permissions
        public static bool CanViewSettings(this ClaimsPrincipal user) => Permissions.Check.CanViewSettings(user.GetAccessLevel());
        public static bool CanEditSettings(this ClaimsPrincipal user) => Permissions.Check.CanEditSettings(user.GetAccessLevel());
        public static bool CanViewMaintenance(this ClaimsPrincipal user) => Permissions.Check.CanViewMaintenance(user.GetAccessLevel());
        public static bool CanEditMaintenance(this ClaimsPrincipal user) => Permissions.Check.CanEditMaintenance(user.GetAccessLevel());
    }
}
